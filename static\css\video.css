/* Video Workshop Styles */

.video-settings {
    background: var(--bg-card);
    border-radius: var(--border-radius-xl);
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid var(--border-primary);
}

.settings-summary {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.setting-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.setting-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.setting-value {
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 0.875rem;
}

.setting-value.enabled {
    background: var(--success-color);
    color: white;
}

.setting-value.disabled {
    background: var(--gray-300);
    color: var(--text-secondary);
}

.ready-memes-section {
    margin-bottom: 3rem;
}

.memes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.meme-preview {
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    padding: 1rem;
    border: 1px solid var(--border-primary);
    transition: all 0.3s ease;
}

.meme-preview:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.meme-image {
    margin-bottom: 1rem;
}

.meme-image img {
    width: 100%;
    max-height: 200px;
    object-fit: contain;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: transform 0.2s ease;
}

.meme-image img:hover {
    transform: scale(1.02);
}

.meme-info {
    color: var(--text-primary);
}

.meme-text {
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
    line-height: 1.4;
}

.audio-preview audio {
    width: 100%;
    height: 32px;
}

.generated-videos-section {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-primary);
}

.videos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.video-card {
    background: var(--bg-card);
    border-radius: var(--border-radius-xl);
    padding: 1.5rem;
    border: 1px solid var(--border-primary);
    transition: all 0.3s ease;
}

.video-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.video-info h4 {
    color: var(--text-primary);
    font-size: 1.125rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.video-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
}

.video-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.video-actions .btn {
    flex: 1;
    min-width: 120px;
    text-align: center;
}

.no-content {
    text-align: center;
    padding: 3rem 1rem;
    background: var(--bg-card);
    border-radius: var(--border-radius-xl);
    border: 1px solid var(--border-primary);
}

.no-content h2 {
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.no-content p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .settings-summary {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .setting-item {
        justify-content: space-between;
    }
    
    .memes-grid {
        grid-template-columns: 1fr;
    }
    
    .videos-grid {
        grid-template-columns: 1fr;
    }
    
    .video-actions {
        flex-direction: column;
    }
    
    .video-actions .btn {
        flex: none;
    }
}

/* Loading states */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn:disabled:hover {
    transform: none;
}

/* Success/Error states */
.alert {
    padding: 1rem;
    border-radius: var(--border-radius);
    margin: 1rem 0;
    font-weight: 500;
}

.alert-success {
    background: var(--success-light);
    color: var(--success-dark);
    border: 1px solid var(--success-color);
}

.alert-error {
    background: var(--danger-light);
    color: var(--danger-dark);
    border: 1px solid var(--danger-color);
}

/* Video preview enhancements */
.video-card h4 {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.video-card:hover h4 {
    -webkit-text-fill-color: var(--primary-color);
}

/* Bulk actions styling */
.bulk-actions {
    margin: 1.5rem 0;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius-lg);
    text-align: center;
}

.bulk-actions .btn {
    min-width: 200px;
    font-size: 1.1rem;
    padding: 0.75rem 1.5rem;
}
