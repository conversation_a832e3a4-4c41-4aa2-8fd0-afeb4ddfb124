<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Workshop</title>
    <link rel="stylesheet" href="/static/css/base.css">
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/memes.css">
    <link rel="stylesheet" href="/static/css/video.css">
</head>
<body>
    <nav>
        <div class="nav-container">
            <a href="/dashboard">🏠 Home</a>
            <a href="/meme_workshop">🎭 Meme Workshop</a>
            <a href="/audio_workshop">🎵 Audio Workshop</a>
            <a href="/video_workshop">🎬 Video Workshop</a>
            <a href="/config">⚙️ Settings</a>
            <a href="/logout">🚪 Logout</a>
        </div>
    </nav>
    <div class="container">
        <!-- Flash Messages -->
        {% if session.flash_message and ('video_workshop' in session.flash_context or 'video' in session.flash_context) %}
        <div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 6px; margin: 20px 0;">
            {{ session.flash_message }}
            {% set _ = session.pop('flash_message') %}
            {% set _ = session.pop('flash_context') %}
        </div>
        {% endif %}

        <h1>🎬 Video Workshop</h1>
        <p>Generate compilation videos from your approved memes and audio.</p>

        <!-- Video Generation Settings -->
        <div class="video-settings">
            <h2>📋 Current Video Settings</h2>
            <div class="settings-summary">
                <div class="setting-item">
                    <span class="setting-label">Regular Videos (16:9):</span>
                    <span class="setting-value {% if config.create_videos %}enabled{% else %}disabled{% endif %}">
                        {% if config.create_videos %}✅ Enabled{% else %}❌ Disabled{% endif %}
                    </span>
                </div>
                <div class="setting-item">
                    <span class="setting-label">Shorts (9:16):</span>
                    <span class="setting-value {% if config.create_shorts %}enabled{% else %}disabled{% endif %}">
                        {% if config.create_shorts %}✅ Enabled{% else %}❌ Disabled{% endif %}
                    </span>
                </div>
                <a href="/config" class="btn btn-secondary">⚙️ Change Settings</a>
            </div>
        </div>

        <!-- Ready Memes Section -->
        {% if memes_by_subreddit %}
        <div class="ready-memes-section">
            <h2>🎯 Memes Ready for Video Generation</h2>
            <p>These memes have been approved and have audio generated. They're ready to be turned into videos.</p>
            
            <!-- Video Generation Button -->
            <div class="bulk-actions">
                <button type="button" class="btn btn-primary" onclick="generateVideos()">🎬 Generate Videos</button>
            </div>

            {% for subreddit, memes in memes_by_subreddit.items() %}
            <div class="subreddit-section">
                <h3>📂 r/{{ subreddit }} ({{ memes|length }} memes ready)</h3>
                
                <div class="memes-grid">
                    {% for meme in memes %}
                    <div class="meme-preview">
                        <div class="meme-image">
                            <img src="/{{ meme.image_path }}" alt="Meme" onclick="openImageModal(this.src)">
                        </div>
                        <div class="meme-info">
                            <div class="meme-text">
                                {% if meme.text %}
                                    <strong>Text:</strong> {{ meme.text[:100] }}{% if meme.text|length > 100 %}...{% endif %}
                                {% else %}
                                    <em>No text (image-only, 3s duration)</em>
                                {% endif %}
                            </div>
                            {% set audio_path = session.get('audio_path_' + meme.id|string) %}
                            {% if audio_path %}
                            <div class="audio-preview">
                                <audio controls style="width: 100%; margin-top: 0.5rem;">
                                    <source src="/{{ audio_path }}" type="audio/wav">
                                </audio>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="no-content">
            <h2>🎯 No Memes Ready for Video Generation</h2>
            <p>You need memes that have been approved in the audio workshop first.</p>
            <a href="/audio_workshop" class="btn btn-primary">🎵 Go to Audio Workshop</a>
        </div>
        {% endif %}

        <!-- Generated Videos Section -->
        {% if generated_videos %}
        <div class="generated-videos-section">
            <h2>📹 Generated Videos</h2>
            <div class="videos-grid">
                {% for video in generated_videos %}
                <div class="video-card">
                    <div class="video-info">
                        <h4>
                            {% if video.video_type == 'shorts' %}
                                📱 Shorts Video
                            {% else %}
                                🎬 Regular Video
                            {% endif %}
                        </h4>
                        <div class="video-stats">
                            <span class="stat">⏱️ {{ "%.1f"|format(video.duration) }}s</span>
                            <span class="stat">🎭 {{ video.memes_count }} memes</span>
                            <span class="stat">📅 {{ video.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                        </div>
                    </div>
                    <div class="video-actions">
                        <a href="/{{ video.video_path }}" class="btn btn-primary" target="_blank">▶️ Preview</a>
                        <a href="/{{ video.video_path }}" class="btn btn-secondary" download>⬇️ Download</a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <div class="actions">
            <a href="/audio_workshop" class="btn btn-secondary">⬅️ Back to Audio Workshop</a>
            <a href="/dashboard" class="btn btn-primary">🏠 Back to Dashboard</a>
        </div>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="image-modal">
        <span class="image-modal-close" onclick="closeImageModal()">&times;</span>
        <img class="image-modal-content" id="modalImage">
    </div>

    <script>
        // Image modal functionality
        function openImageModal(src) {
            document.getElementById('imageModal').style.display = 'block';
            document.getElementById('modalImage').src = src;
        }

        function closeImageModal() {
            document.getElementById('imageModal').style.display = 'none';
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('imageModal');
            if (event.target === modal) {
                closeImageModal();
            }
        }

        // Generate videos function
        function generateVideos() {
            const button = document.querySelector(`button[onclick="generateVideos()"]`);
            const originalText = button.innerHTML;
            
            // Show loading state
            button.innerHTML = '⏳ Generating Videos...';
            button.disabled = true;
            
            fetch('/generate_videos', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: ''
            }).then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message and reload page
                    if (data.message) {
                        alert('Success: ' + data.message);
                    }
                    location.reload();
                } else {
                    alert('Error: ' + (data.message || 'Unknown error'));
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            }).catch(error => {
                console.error('Error:', error);
                alert('Error generating videos. Please try again.');
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }
    </script>
</body>
</html>
