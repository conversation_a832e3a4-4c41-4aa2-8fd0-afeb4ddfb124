/* Settings Layout */
.settings-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

.settings-form {
    background: var(--bg-card);
    border-radius: var(--border-radius-xl);
    padding: 2rem;
    border: 1px solid var(--border-primary);
}

.settings-section {
    margin-bottom: 2.5rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-primary);
}

.settings-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.settings-section h3 {
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    background: var(--bg-secondary);
    border: 2px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: var(--bg-hover);
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

/* Content Types Grid */
.content-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.content-type-card {
    position: relative;
    border: 2px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: all 0.3s ease;
}

.content-type-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.content-type-card input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.content-type-card input[type="checkbox"]:checked + .content-type-label {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-color: var(--primary-color);
}

.content-type-label {
    display: block;
    padding: 1.5rem;
    background: var(--bg-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.content-icon {
    display: block;
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.content-title {
    display: block;
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.content-desc {
    display: block;
    font-size: 0.875rem;
    opacity: 0.8;
}

/* Subreddits Grid */
.subreddits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.subreddit-card {
    position: relative;
    border: 2px solid var(--border-primary);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    transition: all 0.3s ease;
}

.subreddit-card:hover {
    border-color: var(--primary-color);
}

.subreddit-card.custom-subreddit {
    border-color: var(--secondary-color);
}

.subreddit-card input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.subreddit-card input[type="checkbox"]:checked + .subreddit-label {
    background: var(--primary-color);
    color: white;
}

.subreddit-card.custom-subreddit input[type="checkbox"]:checked + .subreddit-label {
    background: var(--secondary-color);
}

.subreddit-label {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 1rem;
    background: var(--bg-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    min-height: 60px; /* Ensure consistent height */
}

.subreddit-name {
    display: block;
    font-weight: 500;
    margin-bottom: 0.25rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
    padding-right: 2rem; /* Add padding to prevent overlap with remove button */
}

.subreddit-link {
    color: inherit;
    text-decoration: none;
    font-size: 0.875rem;
    opacity: 0.8;
}

.remove-subreddit {
    position: absolute;
    top: 0;
    right: 0;
    padding: 0;
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 1.5rem;
    aspect-ratio: 1;
    font-size: 0.875rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 10; /* Ensure it's above other content */
}

.remove-subreddit:hover {
    background: var(--danger-dark);
    transform: scale(1.1);
}

/* Add Subreddit Section */
.add-subreddit-section {
    margin-top: 1.5rem;
    padding: 1.5rem;
    background: var(--bg-secondary);
    border: 2px dashed var(--border-secondary);
    border-radius: var(--border-radius-lg);
}

.add-subreddit-input {
    display: flex;
    gap: 1rem;
    align-items: flex-end;
}

.add-subreddit-input .form-input {
    flex: 1;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-primary);
}

/* Settings Summary */
.settings-summary {
    position: sticky;
    top: 2rem;
    height: fit-content;
}

.summary-card {
    background: var(--bg-card);
    border-radius: var(--border-radius-xl);
    padding: 1.5rem;
    border: 1px solid var(--border-primary);
}

.summary-card h3 {
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.summary-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-primary);
}

.summary-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.summary-section h4 {
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.summary-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.summary-value {
    color: var(--text-primary);
    font-weight: 600;
    background: var(--bg-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius);
}

.summary-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.summary-tag {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    border-radius: var(--border-radius-lg);
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.summary-tag.enabled {
    background: var(--primary-color);
    color: white;
}

.summary-tag.warning {
    background: var(--warning-color);
    color: var(--bg-primary);
}

.summary-tag.subreddit {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
}

.summary-tag.subreddit:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.summary-tag .subreddit-link {
    color: inherit;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .settings-layout {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .settings-summary {
        position: static;
        order: -1;
    }
}

@media (max-width: 768px) {
    .content-types-grid {
        grid-template-columns: 1fr;
    }

    .subreddits-grid {
        grid-template-columns: 1fr;
    }

    .add-subreddit-input {
        flex-direction: column;
        align-items: stretch;
    }

    .form-actions {
        flex-direction: column;
    }
}

/* Subreddit Styles */
.subreddit-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: background-color 0.2s ease;
}

.subreddit-item:hover {
    background-color: var(--gray-100);
}

.subreddit-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.subreddit-link:hover {
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
}

.add-subreddit {
    margin-top: 1rem;
    padding: 1rem;
    background-color: var(--gray-100);
    border-radius: var(--border-radius);
    border: 2px dashed var(--gray-300);
}

.add-subreddit input {
    margin-bottom: 0.5rem;
}

.current-subreddits {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.subreddit-tag {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.subreddit-tag:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

.subreddit-tag .subreddit-link {
    color: white;
    text-decoration: none;
}

.subreddit-tag .subreddit-link:hover {
    color: white;
    background-color: transparent;
}

/* Configuration Form */
.config-form {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.config-section {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--gray-200);
}

.config-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.config-section h3 {
    color: var(--gray-800);
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.config-section-icon {
    color: var(--primary-color);
    font-size: 1.25rem;
}

.config-description {
    color: var(--gray-600);
    font-size: 0.875rem;
    margin-bottom: 1.5rem;
    line-height: 1.5;
}

/* Range Input Styles */
.range-input-container {
    margin: 1rem 0;
}

.range-input {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--gray-200);
    outline: none;
    --webkit-appearance: none;
}

.range-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}

.range-input::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
    box-shadow: var(--shadow-sm);
}

.range-value {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    margin-left: 1rem;
}

/* Subreddit Selection */
.subreddit-selection {
    background: var(--gray-100);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
}

.subreddit-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
    margin-top: 1rem;
}

.subreddit-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    transition: all 0.2s ease;
    cursor: pointer;
}

.subreddit-option:hover {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.subreddit-option input[type="checkbox"] {
    margin: 0;
}

.subreddit-option label {
    margin: 0;
    cursor: pointer;
    font-weight: 500;
    text-transform: none;
    letter-spacing: normal;
    font-size: 0.875rem;
}

/* Custom Subreddit Input */
.custom-subreddit {
    padding: 1.5rem;
    background: white;
    border: 2px dashed var(--gray-300);
    border-radius: var(--border-radius);
    text-align: center;
    transition: all 0.3s ease;
}

.custom-subreddit:hover {
    border-color: var(--primary-color);
    background: var(--gray-50);
}

.custom-subreddit-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: var(--gray-600);
    font-weight: 500;
}

.custom-subreddit-icon {
    font-size: 1.25rem;
    color: var(--primary-color);
}

.custom-input-group {
    display: flex;
    gap: 0.5rem;
    max-width: 400px;
    margin: 0 auto;
}

.custom-input-group input {
    flex: 1;
    margin: 0;
}

.custom-input-group button {
    margin: 0;
    white-space: nowrap;
}

/* Configuration Preview */
.config-preview {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    margin-top: 2rem;
}

.preview-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: var(--gray-800);
    font-weight: 600;
}

.preview-icon {
    color: var(--secondary-color);
    font-size: 1.25rem;
}

.preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.preview-item {
    text-align: center;
    padding: 1rem;
    background: var(--gray-100);
    border-radius: var(--border-radius);
}

.preview-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.preview-label {
    font-size: 0.75rem;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Content Types Selection */
.content-types {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.content-type-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    background: var(--gray-100);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    transition: all 0.2s ease;
    cursor: pointer;
}

.content-type-item:hover {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: white;
}

.content-type-item input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
}

.content-type-item label {
    margin: 0;
    cursor: pointer;
    font-weight: 500;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.current-content-types {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.content-type-tag {
    display: inline-block;
    background-color: var(--secondary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.content-type-tag:hover {
    background-color: var(--secondary-dark);
    transform: translateY(-1px);
}

/* Save Actions */
.save-actions {
    background: var(--gray-100);
    padding: 1.5rem;
    border-radius: var(--border-radius-lg);
    text-align: center;
    margin-top: 2rem;
    border: 1px solid var(--gray-200);
}

.save-actions .btn {
    margin: 0 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .subreddit-grid {
        grid-template-columns: 1fr;
    }

    .custom-input-group {
        flex-direction: column;
    }

    .preview-grid {
        grid-template-columns: 1fr;
    }

    .save-actions .btn {
        display: block;
        margin: 0.5rem 0;
        width: 100%;
    }

    .current-subreddits {
        justify-content: center;
    }
}
