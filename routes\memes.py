from flask import Blueprint, render_template, request, redirect, url_for, session, jsonify
from models import Meme, GeneratedVideo, db
from services.helpers import require_login, get_user_config, save_meme_to_db
from services.reddit_service import get_top_memes

memes_bp = Blueprint('memes', __name__)

@memes_bp.route('/fetch_memes')
@require_login
def fetch_memes():
    """Fetch memes from Reddit"""
    user_id = session['user_id']
    config = get_user_config(user_id)

    # Parse subreddits from config
    subreddits = [s.strip() for s in config.subreddits.split(',') if s.strip()]

    try:
        # Get existing meme URLs to avoid duplicates
        existing_urls = set(meme.url for meme in Meme.query.filter_by(user_id=user_id, discarded=False).all())

        # Fetch memes from Reddit with existing URLs for duplicate detection
        reddit_memes = get_top_memes(limit=config.max_memes, subreddits=subreddits, existing_urls=existing_urls)

        if not reddit_memes:
            session['flash_message'] = "No memes could be fetched from Reddit. Please check your internet connection and try again."
            return redirect(url_for('memes.meme_workshop'))

        new_memes_count = 0
        error_count = 0

        for meme_info in reddit_memes:
            try:
                meme_url = meme_info.get('url', '')

                if not meme_url:
                    error_count += 1
                    continue

                # Save new meme to database with subreddit information
                # No need to check for duplicates here since Reddit service already filtered them
                save_meme_to_db(user_id, meme_url, "", meme_info.get('subreddit', 'memes'))
                new_memes_count += 1

            except Exception:
                error_count += 1
                continue

        # Set flash message for user feedback - only show if there are issues
        if new_memes_count == 0:
            session['flash_message'] = "No new memes could be fetched. They may all be duplicates or there might be a connection issue."

        session['flash_context'] = ['meme_workshop']

    except Exception as e:
        session['flash_message'] = f"Error fetching memes: {str(e)}"
        session['flash_context'] = ['meme_workshop']

    return redirect(url_for('memes.meme_workshop'))

@memes_bp.route('/clear_memes', methods=['GET', 'POST'])
@require_login
def clear_memes():
    """Clear all existing memes for the user"""
    user_id = session['user_id']

    try:
        # Count memes before deletion
        meme_count = Meme.query.filter_by(user_id=user_id, discarded=False).count()

        # Clear existing memes for this user
        Meme.query.filter_by(user_id=user_id).delete()
        db.session.commit()

        # Clear audio session data
        audio_keys_to_remove = [key for key in session.keys() if key.startswith('audio_path_')]
        for key in audio_keys_to_remove:
            session.pop(key, None)

        session['flash_message'] = f"Cleared {meme_count} memes from your collection."
        session['flash_context'] = ['meme_workshop']

    except Exception as e:
        session['flash_message'] = f"Error clearing memes: {str(e)}"
        session['flash_context'] = ['meme_workshop']

    return redirect(url_for('memes.meme_workshop'))

@memes_bp.route('/clear_all_data', methods=['POST'])
@require_login
def clear_all_data():
    """Clear all meme data but preserve user settings"""
    try:
        # Clear all memes
        Meme.query.delete()

        # Note: We no longer clear configurations to preserve user's subreddit settings
        # The user can manually reset their settings in the config page if needed

        db.session.commit()

        # Clear all session data except user_id
        user_id = session.get('user_id')
        session.clear()
        session['user_id'] = user_id

        session['flash_message'] = "All meme data cleared successfully."
        session['flash_context'] = ['dashboard']

    except Exception as e:
        db.session.rollback()
        session['flash_message'] = f"Error clearing data: {str(e)}"
        session['flash_context'] = ['dashboard']

    return redirect(url_for('dashboard.dashboard'))

@memes_bp.route('/meme_workshop')
@require_login
def meme_workshop():
    """Meme Workshop - Combined fetching and editing interface"""
    return render_template('meme_workshop.html')

@memes_bp.route('/review_memes')
@require_login
def review_memes():
    """Review and edit memes - organized by subreddit (only non-approved memes)"""
    user_id = session['user_id']
    # Only show memes that haven't been approved for text yet
    memes = Meme.query.filter_by(user_id=user_id, discarded=False, text_approved=False).all()

    # Group memes by subreddit
    memes_by_subreddit = {}
    for meme in memes:
        if meme.subreddit not in memes_by_subreddit:
            memes_by_subreddit[meme.subreddit] = []
        memes_by_subreddit[meme.subreddit].append(meme)

    return render_template('review_memes.html', memes_by_subreddit=memes_by_subreddit)

@memes_bp.route('/update_meme_text', methods=['POST'])
@require_login
def update_meme_text():
    """Update meme text and clear any existing audio"""
    import os

    user_id = session['user_id']
    meme_id = request.form.get('meme_id')
    new_text = request.form.get('text', '')

    meme = Meme.query.filter_by(id=meme_id, user_id=user_id).first()
    if meme:
        # Check if text actually changed
        text_changed = meme.text != new_text

        meme.text = new_text

        # If text changed, clear any existing audio and reset audio approval
        if text_changed:
            # Clear audio session data
            audio_key = f'audio_path_{meme.id}'
            if audio_key in session:
                # Try to delete the old audio file
                old_audio_path = session[audio_key]
                try:
                    if os.path.exists(old_audio_path):
                        os.remove(old_audio_path)
                except Exception:
                    pass  # Ignore file deletion errors

                # Remove from session
                session.pop(audio_key, None)

            # Reset audio approval since text changed
            meme.audio_approved = False

        db.session.commit()

    return redirect(url_for('memes.review_memes'))

@memes_bp.route('/discard_meme', methods=['POST'])
@require_login
def discard_meme():
    """Discard a meme via AJAX"""
    user_id = session['user_id']
    meme_id = request.form.get('meme_id')

    meme = Meme.query.filter_by(id=meme_id, user_id=user_id).first()
    if meme:
        meme.discarded = True
        db.session.commit()
        return jsonify({'success': True, 'message': 'Meme discarded successfully'})
    else:
        return jsonify({'success': False, 'message': 'Meme not found'}), 404

@memes_bp.route('/approve_meme_text', methods=['POST'])
@require_login
def approve_meme_text():
    """Save text and approve meme for text phase in one action via AJAX"""
    import os

    user_id = session['user_id']
    meme_id = request.form.get('meme_id')
    new_text = request.form.get('text', '')

    meme = Meme.query.filter_by(id=meme_id, user_id=user_id).first()
    if meme:
        # Check if text actually changed
        text_changed = meme.text != new_text

        # Save text and approve in one action
        meme.text = new_text
        meme.text_approved = True

        # If text changed, clear any existing audio and reset audio approval
        if text_changed:
            # Clear audio session data
            audio_key = f'audio_path_{meme.id}'
            if audio_key in session:
                # Try to delete the old audio file
                old_audio_path = session[audio_key]
                try:
                    if os.path.exists(old_audio_path):
                        os.remove(old_audio_path)
                except Exception:
                    pass  # Ignore file deletion errors

                # Remove from session
                session.pop(audio_key, None)

            # Reset audio approval since text changed
            meme.audio_approved = False

        db.session.commit()
        return jsonify({'success': True, 'message': 'Meme approved successfully'})
    else:
        return jsonify({'success': False, 'message': 'Meme not found'}), 404



@memes_bp.route('/audio_workshop')
@require_login
def audio_workshop():
    """Audio Workshop - Show memes approved for audio generation"""
    import os

    user_id = session['user_id']

    # Automatically clean up stale audio session data
    audio_keys_to_remove = []
    for key in list(session.keys()):
        if key.startswith('audio_path_'):
            audio_path = session[key]
            # Remove session entry if file doesn't exist
            if not os.path.exists(audio_path):
                audio_keys_to_remove.append(key)

    for key in audio_keys_to_remove:
        session.pop(key, None)

    # Get memes that are approved for text but not yet processed for audio
    # Only include memes that have text content for audio generation
    memes = Meme.query.filter_by(
        user_id=user_id,
        discarded=False,
        text_approved=True,
        audio_approved=False
    ).filter((Meme.text != None) & (Meme.text != '')).all()

    # Group by subreddit
    memes_by_subreddit = {}
    for meme in memes:
        if meme.subreddit not in memes_by_subreddit:
            memes_by_subreddit[meme.subreddit] = []
        memes_by_subreddit[meme.subreddit].append(meme)

    return render_template('workflow_audio.html', memes_by_subreddit=memes_by_subreddit)

@memes_bp.route('/workflow_audio')
@require_login
def workflow_audio():
    """Legacy route - redirect to audio workshop"""
    return redirect(url_for('memes.audio_workshop'))

@memes_bp.route('/approve_meme_audio', methods=['POST'])
@require_login
def approve_meme_audio():
    """Approve meme audio and move to video generation via AJAX"""
    user_id = session['user_id']
    meme_id = request.form.get('meme_id')

    meme = Meme.query.filter_by(id=meme_id, user_id=user_id).first()
    if meme:
        meme.audio_approved = True
        db.session.commit()
        return jsonify({'success': True, 'message': 'Audio approved successfully'})
    else:
        return jsonify({'success': False, 'message': 'Meme not found'}), 404

@memes_bp.route('/generate_audio', methods=['POST'])
@require_login
def generate_audio():
    """Generate audio for specific meme(s) - handles both individual and bulk generation"""
    from services.audio_service import generate_audio_from_text
    import uuid
    import os

    user_id = session['user_id']
    meme_id = request.form.get('meme_id')  # If provided, generate for specific meme
    generate_all = request.form.get('generate_all')  # If 'true', generate for all

    generated_count = 0
    failed_count = 0

    if meme_id:
        # Generate audio for specific meme
        meme = Meme.query.filter_by(id=meme_id, user_id=user_id, text_approved=True).first()
        if meme and meme.text and meme.text.strip():
            try:
                # Clear any existing audio first
                audio_key = f'audio_path_{meme.id}'
                if audio_key in session:
                    old_audio_path = session[audio_key]
                    try:
                        if os.path.exists(old_audio_path):
                            os.remove(old_audio_path)
                    except Exception:
                        pass  # Ignore file deletion errors
                    session.pop(audio_key, None)

                # Generate unique audio filename
                audio_filename = f"audio_{meme.id}_{uuid.uuid4().hex[:8]}.wav"
                audio_path = os.path.join('static', 'audio', audio_filename)

                # Get the current text from database (ensure it's fresh)
                current_text = meme.text.strip()

                # Generate audio using the current text
                if generate_audio_from_text(current_text, audio_path):
                    # Store audio path in session for review
                    session[f'audio_path_{meme.id}'] = audio_path
                    generated_count = 1
                else:
                    failed_count = 1
                    session['flash_message'] = f"Failed to generate audio for meme {meme.id}."
            except Exception as e:
                failed_count = 1
                session['flash_message'] = f"Error generating audio: {str(e)}"
        else:
            session['flash_message'] = "Invalid meme or no text content."

    elif generate_all == 'true':
        # Generate audio for all approved memes with text
        memes = Meme.query.filter_by(
            user_id=user_id,
            discarded=False,
            text_approved=True,
            audio_approved=False
        ).filter((Meme.text != None) & (Meme.text != '')).all()

        for meme in memes:
            try:
                # Clear any existing audio first (regenerate all)
                audio_key = f'audio_path_{meme.id}'
                if audio_key in session:
                    old_audio_path = session[audio_key]
                    try:
                        if os.path.exists(old_audio_path):
                            os.remove(old_audio_path)
                    except Exception:
                        pass  # Ignore file deletion errors
                    session.pop(audio_key, None)

                # Generate unique audio filename
                audio_filename = f"audio_{meme.id}_{uuid.uuid4().hex[:8]}.wav"
                audio_path = os.path.join('static', 'audio', audio_filename)

                # Get the current text from database (ensure it's fresh)
                current_text = meme.text.strip()

                # Generate audio using the current text
                if generate_audio_from_text(current_text, audio_path):
                    # Store audio path in session for review
                    session[f'audio_path_{meme.id}'] = audio_path
                    generated_count += 1
                else:
                    failed_count += 1
            except Exception:
                failed_count += 1
                continue

        # Set appropriate flash message only for failures
        if generated_count > 0 and failed_count > 0:
            session['flash_message'] = f"Generated audio for {generated_count} memes. {failed_count} failed to generate."
        elif generated_count == 0 and failed_count > 0:
            session['flash_message'] = f"Failed to generate audio for {failed_count} memes."
        elif generated_count == 0 and failed_count == 0:
            session['flash_message'] = "No audio files could be generated."
    else:
        session['flash_message'] = "Invalid request parameters."

    session['flash_context'] = ['audio_workshop', 'workflow_audio']

    # Return JSON response for AJAX requests, otherwise redirect
    if request.headers.get('Content-Type') == 'application/x-www-form-urlencoded':
        # This is likely an AJAX request, return JSON
        if generated_count > 0 and failed_count == 0:
            return jsonify({'success': True})  # No message for successful generation
        elif generated_count > 0 and failed_count > 0:
            return jsonify({'success': True, 'message': f'Generated audio for {generated_count} memes. {failed_count} failed.'})
        elif generated_count == 0 and failed_count > 0:
            return jsonify({'success': False, 'message': f'Failed to generate audio for {failed_count} memes.'})
        else:
            return jsonify({'success': False, 'message': 'No audio files could be generated.'})
    else:
        # Regular form submission, redirect as before
        return redirect(url_for('memes.audio_workshop'))

@memes_bp.route('/video_workshop')
@require_login
def video_workshop():
    """Video Workshop - Show memes ready for video generation and generated videos"""
    user_id = session['user_id']
    config = get_user_config(user_id)

    # Get memes that are approved for audio and ready for video generation
    ready_memes = Meme.query.filter_by(
        user_id=user_id,
        discarded=False,
        text_approved=True,
        audio_approved=True,
        video_generated=False
    ).all()

    # Group by subreddit
    memes_by_subreddit = {}
    for meme in ready_memes:
        if meme.subreddit not in memes_by_subreddit:
            memes_by_subreddit[meme.subreddit] = []
        memes_by_subreddit[meme.subreddit].append(meme)

    # Get generated videos
    generated_videos = GeneratedVideo.query.filter_by(user_id=user_id).order_by(GeneratedVideo.created_at.desc()).all()

    return render_template('video_workshop.html',
                         memes_by_subreddit=memes_by_subreddit,
                         generated_videos=generated_videos,
                         config=config)

@memes_bp.route('/generate_videos', methods=['POST'])
@require_login
def generate_videos():
    """Generate compilation videos based on user settings"""
    from services.video_service import generate_compilation_video, get_audio_duration
    import os

    user_id = session['user_id']
    config = get_user_config(user_id)

    # Get memes ready for video generation
    ready_memes = Meme.query.filter_by(
        user_id=user_id,
        discarded=False,
        text_approved=True,
        audio_approved=True,
        video_generated=False
    ).order_by(Meme.created_at).all()

    if not ready_memes:
        return jsonify({'success': False, 'message': 'No memes ready for video generation.'})

    # Prepare memes data for video generation
    memes_data = []
    for meme in ready_memes:
        # Get audio path from session
        audio_path = session.get(f'audio_path_{meme.id}')

        # Calculate duration
        if audio_path and os.path.exists(audio_path):
            duration = get_audio_duration(audio_path)
        else:
            duration = 3.0  # Default for memes without audio
            audio_path = None

        memes_data.append({
            'meme_id': meme.id,
            'image_path': meme.image_path,
            'audio_path': audio_path,
            'duration': duration
        })

    videos_generated = []
    errors = []

    # Generate regular videos if enabled
    if config.create_videos:
        success, video_path, actual_duration, memes_used = generate_compilation_video(
            memes_data, 'regular', 600
        )

        if success:
            # Save video record
            video_record = GeneratedVideo(
                user_id=user_id,
                video_path=video_path,
                video_type='regular',
                duration=actual_duration,
                memes_count=memes_used
            )
            db.session.add(video_record)

            # Mark memes as video generated
            for i in range(memes_used):
                meme = ready_memes[i]
                meme.video_generated = True

            videos_generated.append(f'Regular video ({memes_used} memes, {actual_duration:.1f}s)')
        else:
            errors.append('Insufficient content for 10-minute regular video')

    # Generate shorts if enabled
    if config.create_shorts:
        success, video_path, actual_duration, memes_used = generate_compilation_video(
            memes_data, 'shorts', 180
        )

        if success:
            # Save video record
            video_record = GeneratedVideo(
                user_id=user_id,
                video_path=video_path,
                video_type='shorts',
                duration=actual_duration,
                memes_count=memes_used
            )
            db.session.add(video_record)

            # Mark memes as video generated (only if not already marked by regular video)
            for i in range(memes_used):
                meme = ready_memes[i]
                meme.video_generated = True

            videos_generated.append(f'Shorts video ({memes_used} memes, {actual_duration:.1f}s)')
        else:
            errors.append('No content available for shorts video')

    # Commit changes
    if videos_generated:
        db.session.commit()

    # Prepare response
    if videos_generated and not errors:
        message = f"Generated: {', '.join(videos_generated)}"
        return jsonify({'success': True, 'message': message})
    elif videos_generated and errors:
        message = f"Generated: {', '.join(videos_generated)}. Errors: {', '.join(errors)}"
        return jsonify({'success': True, 'message': message})
    else:
        message = f"Failed to generate videos. Errors: {', '.join(errors)}"
        return jsonify({'success': False, 'message': message})